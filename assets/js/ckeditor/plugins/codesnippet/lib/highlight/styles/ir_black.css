/*
  IR_Black style (c) <PERSON><PERSON> <<EMAIL>>
*/

.hljs {
  display: block; padding: 0.5em;
  background: #000; color: #f8f8f8;
}

.hljs-shebang,
.hljs-comment,
.hljs-template_comment,
.hljs-javadoc {
  color: #7c7c7c;
}

.hljs-keyword,
.hljs-tag,
.tex .hljs-command,
.hljs-request,
.hljs-status,
.clojure .hljs-attribute {
  color: #96CBFE;
}

.hljs-sub .hljs-keyword,
.method,
.hljs-list .hljs-title,
.nginx .hljs-title {
  color: #FFFFB6;
}

.hljs-string,
.hljs-tag .hljs-value,
.hljs-cdata,
.hljs-filter .hljs-argument,
.hljs-attr_selector,
.apache .hljs-cbracket,
.hljs-date,
.coffeescript .hljs-attribute {
  color: #A8FF60;
}

.hljs-subst {
  color: #DAEFA3;
}

.hljs-regexp {
  color: #E9C062;
}

.hljs-title,
.hljs-sub .hljs-identifier,
.hljs-pi,
.hljs-decorator,
.tex .hljs-special,
.haskell .hljs-type,
.hljs-constant,
.smalltalk .hljs-class,
.hljs-javadoctag,
.hljs-yardoctag,
.hljs-phpdoc,
.nginx .hljs-built_in {
  color: #FFFFB6;
}

.hljs-symbol,
.ruby .hljs-symbol .hljs-string,
.hljs-number,
.hljs-variable,
.vbscript,
.hljs-literal {
  color: #C6C5FE;
}

.css .hljs-tag {
  color: #96CBFE;
}

.css .hljs-rules .hljs-property,
.css .hljs-id {
  color: #FFFFB6;
}

.css .hljs-class {
  color: #FFF;
}

.hljs-hexcolor {
  color: #C6C5FE;
}

.hljs-number {
  color:#FF73FD;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.7;
}
