/* Base16 Atelier Seaside Light - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/seaside/) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */
/* https://github.com/jmblog/color-themes-for-highlightjs */

/* Atelier Seaside Light Comment */
.hljs-comment,
.hljs-title {
  color: #687d68;
}

/* Atelier Seaside Light Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #e6193c;
}

/* Atelier Seaside Light Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-pragma,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #87711d;
}

/* Atelier Seaside Light Yellow */
.hljs-ruby .hljs-class .hljs-title,
.css .hljs-rules .hljs-attribute {
  color: #c3c322;
}

/* Atelier Seaside Light Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #29a329;
}

/* Atelier Seaside Light Aqua */
.css .hljs-hexcolor {
  color: #1999b3;
}

/* Atelier Seaside Light Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #3d62f5;
}

/* Atelier Seaside Light Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #ad2bee;
}

.hljs {
  display: block;
  background: #f0fff0;
  color: #5e6e5e;
  padding: 0.5em;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
