/*

Style with support for rainbow parens

*/

.hljs {
  display: block; padding: 0.5em;
  background: #474949; color: #D1D9E1;
}


.hljs-body,
.hljs-collection {
   color: #D1D9E1;
}

.hljs-comment,
.hljs-template_comment,
.diff .hljs-header,
.hljs-doctype,
.lisp .hljs-string,
.hljs-javadoc {
  color: #969896;
  font-style: italic;
}

.hljs-keyword,
.clojure .hljs-attribute,
.hljs-winutils,
.javascript .hljs-title,
.hljs-addition,
.css .hljs-tag {
  color: #cc99cc;
}

.hljs-number { color: #f99157; }

.hljs-command,
.hljs-string,
.hljs-tag .hljs-value,
.hljs-phpdoc,
.tex .hljs-formula,
.hljs-regexp,
.hljs-hexcolor {
  color: #8abeb7;
}

.hljs-title,
.hljs-localvars,
.hljs-function .hljs-title,
.hljs-chunk,
.hljs-decorator,
.hljs-built_in,
.lisp .hljs-title,
.hljs-identifier
{
  color: #b5bd68;
}

.hljs-class .hljs-keyword
{
  color: #f2777a;
}

.hljs-variable,
.lisp .hljs-body,
.smalltalk .hljs-number,
.hljs-constant,
.hljs-class .hljs-title,
.hljs-parent,
.haskell .hljs-label,
.hljs-id,
.lisp .hljs-title,
.clojure .hljs-title .hljs-built_in {
   color: #ffcc66;
}

.hljs-tag .hljs-title,
.hljs-rules .hljs-property,
.django .hljs-tag .hljs-keyword,
.clojure .hljs-title .hljs-built_in {
  font-weight: bold;
}

.hljs-attribute,
.clojure .hljs-title {
  color: #81a2be;
}

.hljs-preprocessor,
.hljs-pragma,
.hljs-pi,
.hljs-shebang,
.hljs-symbol,
.hljs-symbol .hljs-string,
.diff .hljs-change,
.hljs-special,
.hljs-attr_selector,
.hljs-important,
.hljs-subst,
.hljs-cdata {
  color: #f99157;
}

.hljs-deletion {
  color: #dc322f;
}

.tex .hljs-formula {
  background: #eee8d5;
}
