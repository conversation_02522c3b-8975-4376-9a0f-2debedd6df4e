/*
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/

.cke_balloon.cke_balloontoolbar span:last-child a:last-child::after,
.cke_balloon.cke_balloontoolbar span:last-child a:last-child:hover::after,
.cke_balloon.cke_balloontoolbar span:last-child::after
{
	border-right: 0;
}

.cke_balloon.cke_balloontoolbar .cke_combo
{
	margin-bottom: 0;
	margin-right: 2px;
}

.cke_balloon.cke_balloontoolbar .cke_combo:first-child a.cke_combo_button
{
	margin-left: 0;
}

.cke_balloon.cke_balloontoolbar .cke_combo:last-child
{
	margin-right: 0;
}

/* Negative value for left margin is needed to overlap separator (#2535). */
.cke_balloon.cke_balloontoolbar .cke_combo a.cke_combo_button
{
	margin: 0 1px 0 -2px;
}

/* Combo states (#1682). */
.cke_balloon.cke_balloontoolbar .cke_combo_on a.cke_combo_button,
.cke_balloon.cke_balloontoolbar .cke_combo_off a.cke_combo_button:hover,
.cke_balloon.cke_balloontoolbar .cke_combo_off a.cke_combo_button:focus,
.cke_balloon.cke_balloontoolbar .cke_combo_off a.cke_combo_button:active
{
	border: none;
	padding: 1px;
	outline: 1px solid #bcbcbc;
}
