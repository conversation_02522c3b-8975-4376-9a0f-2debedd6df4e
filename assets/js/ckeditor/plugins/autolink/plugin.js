﻿/*
 Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
 For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
(function(){CKEDITOR.plugins.add("autolink",{requires:"clipboard,textmatch,link",isSupportedEnvironment:function(){return!CKEDITOR.env.ie||CKEDITOR.env.edge},init:function(b){function e(a){a=CKEDITOR.tools.htmlDecodeAttr(a);var c=new CKEDITOR.dom.element("a"),d=a.replace(/"/g,"%22"),d=d.match(b.config.autolink_urlRegex)?d:"mailto:"+d;c.setText(a);c.setAttribute("href",d);a=CKEDITOR.plugins.link.parseLinkAttributes(b,c);a=CKEDITOR.plugins.link.getLinkAttributes(b,a);CKEDITOR.tools.isEmpty(a.set)||
c.setAttributes(a.set);a.removed.length&&c.removeAttributes(a.removed);c.removeAttribute("data-cke-saved-href");return c.getOuterHtml()}function g(a,c){var b=a.slice(0,c).split(/\s+/);return(b=b[b.length-1])&&f(b)?{start:a.lastIndexOf(b),end:c}:null}function f(a){return a.match(b.config.autolink_urlRegex)||a.match(b.config.autolink_emailRegex)}this.isSupportedEnvironment()&&(b.on("paste",function(a){if(a.data.dataTransfer.getTransferType(b)!=CKEDITOR.DATA_TRANSFER_INTERNAL){var c=a.data.dataValue;
-1<c.indexOf("\x3c")||!f(c)||(a.data.dataValue=e(c),a.data.type="html")}}),b.on("key",function(a){if("wysiwyg"===b.mode&&-1!=CKEDITOR.tools.indexOf(b.config.autolink_commitKeystrokes,a.data.keyCode)){var c=CKEDITOR.plugins.textMatch.match(b.getSelection().getRanges()[0],g);if(c&&(a=b.getSelection(),!a.getRanges()[0].startContainer.getAscendant("a",!0)&&(a.selectRanges([c.range]),b.insertHtml(e(c.text),"text"),!CKEDITOR.env.webkit))){var c=a.getRanges()[0],d=b.createRange();d.setStartAfter(c.startContainer);
a.selectRanges([d])}}}))}});CKEDITOR.config.autolink_commitKeystrokes=[13,32];CKEDITOR.config.autolink_urlRegex=/^(https?|ftp):\/\/(-\.)?([^\s\/?\.#]\.?)+(\/[^\s]*)?[^\s\.,]$/i;CKEDITOR.config.autolink_emailRegex=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/})();