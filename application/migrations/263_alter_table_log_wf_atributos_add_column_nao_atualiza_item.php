<?php

class Migration_Alter_Table_Log_Wf_Atributos_Add_Column_Nao_Atualiza_Item extends CI_Migration
{

    private $_table = "log_wf_atributos";
    public function up()
    {
        if ($this->db->table_exists($this->_table)) {
            $fields = array();

            if (!$this->db->field_exists('nao_atualiza_item', $this->_table)) {
                $fields['nao_atualiza_item'] = array(
                    'type'           => 'tinyint(1)',
                    'null'           => TRUE,
                    'default'        => 0
                );
            }

            if (!empty($fields)) $this->dbforge->add_column($this->_table, $fields);
        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {
            if ($this->db->field_exists('nao_atualiza_item', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'nao_atualiza_item');
            }
        }
    }
}
