<?php

class Migration_Create_Log_Wf_Atributos_Update_Data_Modificacao_Item_Trigger extends CI_Migration
{

    public function up()
    {
        $this->db->query("DROP TRIGGER IF EXISTS `log_wf_atributos_after_update`");

        $this->db->query("CREATE TRIGGER `log_wf_atributos_after_update`
                            AFTER INSERT ON log_wf_atributos
                            FOR EACH ROW
                            BEGIN
                                DECLARE diff_seconds INT;
                                DECLARE item_modification_time TIMESTAMP;
                                IF NEW.nao_atualiza_item = 0 THEN
                                    SELECT data_modificacao INTO item_modification_time
                                    FROM item
                                    WHERE part_number = NEW.part_number
                                    AND id_empresa = NEW.id_empresa
                                    AND estabelecimento = NEW.estabelecimento;

                                    IF item_modification_time IS NULL THEN
                                        SET diff_seconds = 0;
                                    ELSE
                                        SET diff_seconds = TIMESTAMPDIFF(SECOND, item_modification_time, CURRENT_TIMESTAMP);
                                    END IF;

                                    IF diff_seconds > 2 OR item_modification_time IS NULL THEN
                                        UPDATE item
                                        SET data_modificacao = CURRENT_TIMESTAMP
                                        WHERE part_number = NEW.part_number
                                        AND id_empresa = NEW.id_empresa
                                        AND estabelecimento = NEW.estabelecimento;
                                    END IF;
                                END IF;
                            END
                        ");
    }

    public function down()
    {
        $this->db->query("DROP TRIGGER IF EXISTS `log_wf_atributos_after_update`");
    }
}
