<?php

class Migration_Alter_Table_Cad_Item_Attr_Remove_After_Update_Trigger extends CI_Migration
{

    public function up()
    {
        $this->db->query("DROP TRIGGER IF EXISTS `cad_item_attr_after_update`");
    }

    public function down()
    {
        $this->db->query("DROP TRIGGER IF EXISTS `cad_item_attr_after_update`");

        $this->db->query("CREATE TRIGGER `cad_item_attr_after_update` AFTER
                            UPDATE ON `cad_item_attr` FOR EACH ROW 
                            BEGIN
                                UPDATE
                                    item i
                                INNER JOIN cad_item ci ON
                                    ci.part_number = i.part_number
                                    AND ci.id_empresa = i.id_empresa
                                    AND ci.estabelecimento = i.estabelecimento
                                SET
                                    i.data_modificacao = CURRENT_TIMESTAMP
                                WHERE
                                    ci.id_item = NEW.id_item;
                            END;
                        ");
    }
}