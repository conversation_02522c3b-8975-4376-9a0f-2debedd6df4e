<?php

class Migration_Alter_Table_Empresa_Add_Column_Financeiro extends CI_Migration
{
    private $_table = "empresa";

    public function up()
    {
        if ($this->db->table_exists($this->_table)) {

            if (!$this->db->field_exists('habilitar_uso_franquia', $this->_table)) {
                $fields = array(
                    'habilitar_uso_franquia' => array(
                        'type' => 'boolean',
                        'default' => 0
                    )
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('quantidade_franquia_mensal', $this->_table)) {
                $fields = array(
                    'quantidade_franquia_mensal' => array(
                        'type' => 'INT',
                        'constraint' => 10,
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('habilitar_cobrancas_adicionais', $this->_table)) {
                $fields = array(
                    'habilitar_cobrancas_adicionais' => array(
                        'type' => 'boolean',
                        'default' => 0
                    )
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('valor_padrao_default', $this->_table)) {
                $fields = array(
                    'valor_padrao_default' => array(
                        'type' => 'decimal(17,2)',
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('valor_quimico_default', $this->_table)) {
                $fields = array(
                    'valor_quimico_default' => array(
                        'type' => 'decimal(17,2)',
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('habilitar_notificacoes', $this->_table)) {
                $fields = array(
                    'habilitar_notificacoes' => array(
                        'type' => 'boolean',
                        'default' => 0
                    )
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('destinatarios_excedente', 'empresa')) {
                $fields = array(
                    'destinatarios_excedente' => array(
                        'type' => 'TEXT', 
                        'null' => TRUE
                    ),
                );

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('percentual_primeira_notificacao', 'empresa')) {
                $fields = array(
                    'percentual_primeira_notificacao' => array(
                        'type' => 'INT',
                        'constraint' => 2,
                        'null' => TRUE
                    )
                );

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('percentual_segunda_notificacao', 'empresa')) {
                $fields = array(
                    'percentual_segunda_notificacao' => array(
                        'type' => 'INT',
                        'constraint' => 3,
                        'null' => TRUE
                    )
                );

                $this->dbforge->add_column($this->_table, $fields);
            }


            if (!$this->db->field_exists('habilitar_bloqueio', $this->_table)) {
                $fields = array(
                    'habilitar_bloqueio' => array(
                        'type' => 'boolean',
                        'default' => 0
                    )
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('tipo_bloqueio', $this->_table)) {
                $fields = array(
                    'tipo_bloqueio' => array(
                        'type' => 'boolean',
                        'default' => 0
                    )
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            
            if (!$this->db->field_exists('percentual_excedente', $this->_table)) {
                $fields = array(
                    'percentual_excedente' => array(
                        'type' => 'INT',
                        'constraint' => 11,
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('info_notificacao', $this->_table)) {
                $fields = array(
                    'info_notificacao' => array(
                        'type' => 'INT',
                        'constraint' => 8,
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }
        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {

            if ($this->db->field_exists('habilitar_uso_franquia', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'habilitar_uso_franquia');
            }
            if ($this->db->field_exists('quantidade_franquia_mensal', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'quantidade_franquia_mensal');
            }
            if ($this->db->field_exists('habilitar_cobrancas_adicionais', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'habilitar_cobrancas_adicionais');
            }
            if ($this->db->field_exists('habilitar_bloqueio', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'habilitar_bloqueio');
            }
            if ($this->db->field_exists('tipo_bloqueio', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'tipo_bloqueio');
            }
            if ($this->db->field_exists('percentual_excedente', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'percentual_excedente');
            }
            if ($this->db->field_exists('valor_padrao_default', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'valor_padrao_default');
            }
            if ($this->db->field_exists('valor_quimico_default', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'valor_quimico_default');
            }
            if ($this->db->field_exists('habilitar_notificacoes', $this->_table)) {
                $this->dbforge->drop_column($this->_table, 'habilitar_notificacoes');
            }
            if ($this->db->field_exists('destinatarios_excedente', 'empresa')) {
                $this->dbforge->drop_column($this->_table, 'destinatarios_excedente');
            }   
            if ($this->db->field_exists('percentual_primeira_notificacao', 'empresa')) {
                $this->dbforge->drop_column($this->_table, 'percentual_primeira_notificacao');
            }
            if ($this->db->field_exists('percentual_segunda_notificacao', 'empresa')) {
                $this->dbforge->drop_column($this->_table, 'percentual_segunda_notificacao');
            } 

        }
    }
}