<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Permissao_Prioridades extends CI_Migration {

    public function up()
    {
        $this->db->where('slug', 'gerenciar_prioridades');
        $g_query = $this->db->get('permissao', 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Gerenciar - Prioridades',
                'slug'      => 'gerenciar_prioridades',
                'pagina'      => '1'
            ));
        }
    }

    public function down()
    {
        $this->db->where('slug', 'gerenciar_prioridades');
        $query = $this->db->get('permissao');

        $id_permissao = array();

        foreach ($query->result() as $row) {
            $id_permissao[] = $row->id_permissao;
        }

        if (count($id_permissao))
        {
            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('perfil_permissao');

            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('permissao');
        }
    }

}
