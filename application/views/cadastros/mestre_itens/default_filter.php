<?php echo form_open("cadastros/mestre_itens?search=", array('class' => 'form-inline', 'method' => 'post', 'name' => 'search_form', 'id' => 'search_form_id')); ?>
<input type="hidden" name="filtered" value="1" />
<div class="row">
    <div class="col-sm-3">
        <label for="status">Status</label>
        <div class="form-group" style="width: 100%">
            <?php $status_selected = $this->item_model->get_state('filter.status') ?>
            <select class="form-control selectpicker" multiple title="Todos os Status" data-count-selected-text="{0} status selecionados" data-selected-text-format="count" name="status[]" id="status" data-width="100%">

                <?php foreach ($lista_status as $status_item) : ?>
                    <?php if ($this->input->post('status') !== false) :
                        $selected = is_array($status_selected) ? in_array($status_item['status'], $status_selected) : $status_item['status'] == $status_selected; ?>
                        <option value="<?php echo $status_item['status'] ?>" <?php echo $selected ? 'selected' : null; ?>><?php echo $status_item['status_formatado'] ?></option>
                    <?php else : ?>
                        <option <?php echo is_array($this->input->get('status')) ? set_select('status', $status_item['status'],   in_array($status_item['status'], $this->input->get('status'))) : '' ?> value="<?php echo $status_item['status'] ?>"><?php echo $status_item['status_formatado'] ?></option>
                    <?php endif; ?>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="col-sm-3">
        <label for="evento">Pacote / Evento</label>
        <div class="form-group" style="width: 100%">

            <select
                class="form-control selectpicker"
                data-selected-text-format="count > 1"
                data-count-selected-text="Eventos ({0})"
                multiple
                data-actions-box="true"
                title="[PACOTE / EVENTO]"
                data-deselect-all-text="Nenhum"
                data-select-all-text="Todos"
                name="evento[]"
                data-live-search="true"
                id="evento"
                data-width="100%">

                <option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>

            </select>

        </div>
    </div>
    <div class="col-sm-3">
        <label for="data_ini">Data Inicial Criação</label>
        <div class="form-group" style="width: 100%">
            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini">
                <?php if ($this->input->post('data_ini') !== false) :
                ?>
                    <input type="text" class="form-control datetimepicker" name="data_ini" id="data_ini" value="<?php echo $this->input->post('data_ini') ?>" placeholder="Data inicial" />
                <?php else : ?>
                    <input type="text" class="form-control datetimepicker" name="data_ini" id="data_ini" value="<?php echo $this->input->post('data_ini') ?>" placeholder="Data inicial" />
                <?php endif; ?>
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
            </div>
        </div>
    </div>
    <div class="col-sm-3">
        <label for="data_fim">Data Final Criação</label>
        <div class="form-group" style="width: 100%">
            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_fim">
                <?php if ($this->input->post('data_fim') !== false) :
                ?>
                    <input type="text" class="form-control datetimepicker" name="data_fim" id="data_fim" value="<?php echo $this->input->post('data_fim') ?>" placeholder="Data final" />
                <?php else : ?>
                    <input type="text" class="form-control datetimepicker" name="data_fim" id="data_fim" value="<?php echo $this->input->post('data_fim') ?>" placeholder="Data final" />
                <?php endif; ?>

                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
            </div>
        </div>
    </div>
</div>
<br>
<div class="row">
    <?php if (isset($has_status_exportacao) && $has_status_exportacao): ?>
        <div class="col-sm-3">
            <label for="statusExportacao">Status Exportação</label>
            <div class="form-group" style="width: 100%" data-toggle="tooltip" title="O filtro será apenas aplicado para itens já atribuidos">
                <select
                    class="form-control selectpicker"
                    placeholder="Status Exportação"
                    title="Status Exportação"
                    name="statusExportacao"
                    id="statusExportacao" data-width="100%">
                    <?php $exportacao_selected = $this->item_model->get_state('filter.statusExportacao') ?>
                    <?php if ($this->input->post('statusExportacao') !== false) : ?>
                        <option <?php echo empty($exportacao_selected) ? 'selected' : '' ?> value="-1">Todos os status</option>
                        <option <?php echo $this->input->post('statusExportacao') == 'pendente' ? 'selected' : '' ?> value="pendente">Pendente</option>
                        <option <?php echo $this->input->post('statusExportacao') == 'exportado' ? 'selected' : '' ?> value="exportado">Exportado</option>
                    <?php else : ?>
                        <option <?php echo empty($exportacao_selected) ? 'selected' : '' ?> value="-1">Todos os status</option>
                        <option <?php echo $this->input->get('statusExportacao') == 'pendente' ? 'selected' : '' ?> value="pendente">Pendente</option>
                        <option <?php echo $this->input->get('statusExportacao') == 'exportado' ? 'selected' : '' ?> value="exportado">Exportado</option>
                    <?php endif; ?>
                </select>
            </div>
        </div>
    <?php endif; ?>

    <?php if (in_array('owner', $campos_adicionais)) : ?>
        <div class="col-sm-3">
            <label for="owner">Owner</label>
            <div class="form-group" style="width: 100%">
                <select
                    class="selectpicker form-control"
                    style="width: 100%"
                    name="owner[]"
                    id="owner"
                    data-live-search="true"
                    multiple
                    data-selected-text-format="count > 1"
                    data-count-selected-text="Owner ({0})"
                    title="Selecione Owner">
                    <option value="-1">Todos os owners</option>
                </select>
            </div>
        </div>
    <?php endif; ?>

    <div class="col-sm-3">
        <label for="prioridade">Prioridade</label>
        <div class="form-group" style="width: 100%">
            <select
                class="selectpicker form-control"
                style="width: 100%"
                name="prioridade[]"
                id="prioridade"
                data-live-search="true"
                multiple
                data-selected-text-format="count > 1"
                data-count-selected-text="Prioridade ({0})"
                title="Selecione a Prioridade">
                <option value="-1">Todos as prioridades</option>
            </select>
        </div>
    </div>

    <?php if ($hasStatusTriagemDiana && has_role('consultor')) : ?>
        <div class="col-sm-3">
            <label for="triagem_diana_toggle">Triagem DIANA</label>
            <div class="form-group" style="width: 100%">
                <?php $triagem_diana_falha = $this->input->post('triagem_diana_falha') ?: $this->item_model->get_state('filter.triagem_diana_falha');  ?>
                <div class="toggle-switch">
                    <!-- O input hidden é necessário para garantir que o valor '0' seja enviado quando o checkbox estiver desmarcado -->
                    <input type="hidden" name="triagem_diana_falha" value="0">
                    <input
                        type="checkbox"
                        id="triagem_diana_falha"
                        name="triagem_diana_falha"
                        value="1"
                        <?php echo $triagem_diana_falha === '1' ? 'checked' : null; ?>
                        class="toggle-checkbox">
                    <label for="triagem_diana_falha" class="toggle-label">
                        <span class="toggle-inner"></span>
                        <span class="toggle-switch-text">Falha na triagem</span>
                    </label>
                </div>
            </div>
        </div>
    <?php endif; ?>

</div>
<br>
<div class="row">
    <div class="col-sm-12">
        <label for="search" style="width: 100%">Pesquisar:</label>
        <div class="form-group" style="width: 100%">

            <?php
            // $search = $this->input->post('search') ? $this->input->post('search') : $this->input->get('search');
            // $search = htmlentities($search, ENT_QUOTES, 'UTF-8');
            // $search = urldecode($search);
            // $search = nl2br($search);
            // $search = str_replace("<br />", "", $search);
            $search = $this->item_model->get_state('filter.search');
            ?>
            <textarea style="width: 100%" class="form-control" rows="3" placeholder="Pesquisar..." name="search" id="search" value="<?php echo $search ?>"><?php echo $search ?></textarea>
        </div>
    </div>
</div>
<br />
<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
    <div class="panel panel-default">
        <div class="panel-heading" role="tab" id="headingOne">
            <h4 class="panel-title">
                <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                    <span class="glyphicon glyphicon-filter" aria-hidden="true"></span> Filtros Avançados
                </a>
            </h4>
        </div>
        <div id="collapseOne" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingOne">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-4">
                        <label for="novo_material_modal">Novo Material</label>
                        <div class="form-group" style="width: 100%">
                            <select class="form-control selectpicker" placeholder="Novo Material" title="Todos os status" name="novo_material_modal" id="novo_material_modal" data-width="100%">
                                <?php if ($this->input->post('novo_material_modal') !== false) : ?>
                                    <option <?php echo empty($this->input->post('novo_material_modal')) ? 'selected' : '' ?> value="-1">Todos os status</option>
                                    <option <?php echo $this->input->post('novo_material_modal') == 'S' ? 'selected' : '' ?> value="S">SIM</option>
                                    <option <?php echo $this->input->post('novo_material_modal') == 'N' ? 'selected' : '' ?> value="N">NÃO</option>
                                <?php else : ?>
                                    <option <?php echo empty($this->input->get('novo_material_modal')) ? 'selected' : '' ?> value="-1">Todos os status</option>
                                    <option <?php echo $this->input->get('novo_material_modal') == 'S' ? 'selected' : '' ?> value="S">SIM</option>
                                    <option <?php echo $this->input->get('novo_material_modal') == 'N' ? 'selected' : '' ?> value="N">NÃO</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label for="estabelecimento_modal">Estabelecimento</label>
                        <div class="form-group" style="width: 100%">
                            <?php $estabelecimento_selected = $this->item_model->get_state('filter.estabelecimento_modal') ?>
                            <select class="selectpicker form-control" style="width: 100%" name="estabelecimento_modal[]" id="estabelecimento_modal" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Estabelecimento ({0})" title="Selecione o estabelecimento">
                                <option <?php echo empty($estabelecimento_selected) ? 'selected' : '' ?> value="-1">Todos os estabelecimentos</option>
                                <?php foreach ($estabelecimentos as $estabelecimento) : ?>
                                    <?php if ($this->input->post('estabelecimento_modal') !== false) :
                                        $selected = is_array($estabelecimento_selected) ? in_array($estabelecimento, $estabelecimento_selected) : $estabelecimento == $estabelecimento_selected; ?>
                                        <option value="<?php echo $estabelecimento ?>" <?php echo $selected ? 'selected' : null; ?>>
                                            <?php echo $estabelecimento; ?>
                                        </option>
                                    <?php else :
                                        $selected = false;
                                        $selected_values = $this->input->get('estabelecimento_modal');
                                        if (!empty($selected_values)) {
                                            foreach ($selected_values as $selected_value) {
                                                if ($selected_value === $estabelecimento) {
                                                    $selected = true;
                                                    break;
                                                }
                                            }
                                        }
                                    ?>
                                        <option <?php echo $selected ? 'selected' : ''; ?> value="<?php echo $estabelecimento; ?>">
                                            <?php echo $estabelecimento; ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>

                            </select>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label for="ncm_proposta_modal">NCM Proposto</label>
                        <div class="form-group" style="width: 100%">

                            <select
                                class="selectpicker form-control"
                                style="width: 100%"
                                name="ncm_proposta_modal[]"
                                id="ncm_proposta_modal"
                                data-live-search="true"
                                multiple
                                data-selected-text-format="count > 1"
                                data-count-selected-text="NCM-Proposto({0})"
                                title="Selecione o NCM">
                                <option value="-1">Todos os ncms propostos</option>

                            </select>
                        </div>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-sm-4">
                        <label for="sistema_origem_modal">Sistema de Origem</label>
                        <div class="form-group" style="width: 100%">

                            <select
                                class="selectpicker form-control"
                                style="width: 100%"
                                name="sistema_origem_modal[]"
                                id="sistema_origem_modal"
                                data-live-search="true"
                                multiple
                                data-selected-text-format="count > 1"
                                data-count-selected-text="Sistema de Origem({0})"
                                title="Selecione o sistema de origem">
                                <option value="-1">Todos os sistemas de origem</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label for="ex_ipi_modal">Ex-IPI</label>
                        <div class="form-group" style="width: 100%">

                            <select
                                class="selectpicker form-control"
                                style="width: 100%"
                                name="ex_ipi_modal[]"
                                id="ex_ipi_modal"
                                data-live-search="true"
                                multiple
                                data-selected-text-format="count > 1"
                                data-count-selected-text="Ex-IPI({0})"
                                title="Selecione o Ex IPI">
                                <option value="-1">Todos os Ex IPI</option>

                            </select>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label for="ex_ii_modal">Ex-II</label>
                        <div class="form-group" style="width: 100%">
                            <select
                                class="selectpicker form-control"
                                style="width: 100%"
                                name="ex_ii_modal[]"
                                id="ex_ii_modal"
                                data-live-search="true"
                                multiple
                                data-selected-text-format="count > 1"
                                data-count-selected-text="Ex-II({0})"
                                title="Selecione o Ex II">
                                <option value="-1">Todos os Ex II</option>

                            </select>
                        </div>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-sm-6">
                        <label for="descricao_completa_modal">Descrição Completa</label>
                        <div class="form-group" style="width: 100%">
                            <?php
                            $descricao_completa_modal = $this->input->post('descricao_completa_modal') ? $this->input->post('descricao_completa_modal') : $this->input->get('descricao_completa_modal');
                            ?>
                            <textarea class="form-control" rows="3" style="width: 100%" name="descricao_completa_modal" id="descricao_completa_modal"><?= $descricao_completa_modal ?></textarea>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label for="descricao_global_modal">Descrição Global</label>
                        <div class="form-group" style="width: 100%">
                            <?php
                            $descricao_global_modal = $this->input->post('descricao_global_modal') ? $this->input->post('descricao_global_modal') : $this->input->get('descricao_global_modal');
                            ?>
                            <textarea class="form-control" rows="3" style="width: 100%" name="descricao_global_modal" id="descricao_global_modal"><?= $descricao_global_modal ?></textarea>
                        </div>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-sm-3">
                        <label for="data_inicio_modificacao_modal">Data Inicial Modificação</label>
                        <div class="form-group" style="width: 100%">
                            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini_modal">
                                <?php if ($this->input->post('data_inicio_modificacao_modal') !== false) : ?>
                                    <input type="text" class="form-control datetimepicker" name="data_inicio_modificacao_modal" id="data_inicio_modificacao_modal" value="<?php echo $this->input->post('data_inicio_modificacao_modal') ?>" placeholder="" />
                                <?php else : ?>
                                    <input type="text" class="form-control datetimepicker" name="data_inicio_modificacao_modal" id="data_inicio_modificacao_modal" value="<?php echo $this->input->post('data_inicio_modificacao_modal') ?>" placeholder="" />
                                <?php endif; ?>

                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label for="data_fim_modificacao_modal">Data Final Modificação</label>
                        <div class="form-group" style="width: 100%">
                            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_fim_modal">
                                <?php if ($this->input->post('data_fim_modificacao_modal') !== false) : ?>
                                    <input type="text" class="form-control datetimepicker" name="data_fim_modificacao_modal" id="data_fim_modificacao_modal" value="<?php echo $this->input->post('data_fim_modificacao_modal') ?>" placeholder="" />
                                <?php else : ?>
                                    <input type="text" class="form-control datetimepicker" name="data_fim_modificacao_modal" id="data_fim_modificacao_modal" value="<?php echo $this->input->post('data_fim_modificacao_modal') ?>" placeholder="" />
                                <?php endif; ?>

                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <label for="novo_importado">Importado</label>
                        <div class="form-group" style="width: 100%">
                            <select class="form-control selectpicker" placeholder="Importado" title="Todos os status" name="novo_importado" id="novo_importado" data-width="100%">
                                <?php if ($this->input->post('novo_importado') !== false) : ?>
                                    <option <?php echo empty($this->input->post('novo_importado')) ? 'selected' : '' ?> value="-1">Todos os status</option>
                                    <option <?php echo $this->input->post('novo_importado') == 'S' ? 'selected' : '' ?> value="S">SIM</option>
                                    <option <?php echo $this->input->post('novo_importado') == 'N' ? 'selected' : '' ?> value="N">NÃO</option>
                                <?php else : ?>
                                    <option <?php echo empty($this->input->get('novo_importado')) ? 'selected' : '' ?> value="-1">Todos os status</option>
                                    <option <?php echo $this->input->get('novo_importado') == 'S' ? 'selected' : '' ?> value="S">SIM</option>
                                    <option <?php echo $this->input->get('novo_importado') == 'N' ? 'selected' : '' ?> value="N">NÃO</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <label for="novo_status_atributos">Status de Atributos</label>
                        <div class="form-group" style="width: 100%">
                            <?php $status_atributos_selected = $this->item_model->get_state('filter.novo_status_atributos') ?>
                            <select class="selectpicker form-control" placeholder="Status de atributos" style="width: 100%" name="novo_status_atributos[]" id="novo_status_atributos" data-live-search="true" multiple data-selected-text-format="count > 1" data-count-selected-text="Status de atributos({0})" title="Selecione o status de atributos">

                                <option <?php echo empty($this->input->post('novo_status_atributos')) ? 'selected' : '' ?> value="-1">Todos os status</option>
                                <?php foreach ($status_todos_atributos as $status_atributos) : ?>
                                    <?php if ($this->input->post('novo_status_atributos') !== false) :
                                        $selected = is_array($status_atributos_selected) ? in_array($status_atributos->id, $status_atributos_selected) : $status_atributos->id == $status_atributos_selected; ?>
                                        <option <?php echo $selected ? 'selected' : ''; ?> value="<?php echo $status_atributos->id; ?>">
                                            <?php echo $status_atributos->status; ?>
                                        </option>
                                    <?php else : ?>
                                        <option <?php echo is_array($this->input->get('novo_status_atributos')) ? set_select('novo_status_atributos', $status_atributos->id,   in_array($status_atributos->id, $this->input->get('novo_status_atributos'))) : '' ?> value="<?php echo $status_atributos->id; ?>">
                                            <?php echo $status_atributos->status; ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <br>

                <!-- Filtro de data de homologação incluído na feature 672 - wave 2 - linha 45 -->
                <div class="row">
                    <div class="col-sm-3">
                        <label for="data_inicio_homologacao_modal">Data Inicial Homologação</label>
                        <div class="form-group" style="width: 100%">
                            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_ini_homologacao_modal">
                                <?php if ($this->input->post('data_inicio_homologacao_modal') !== false) : ?>
                                    <input
                                        type="text"
                                        class="form-control datetimepicker"
                                        name="data_inicio_homologacao_modal"
                                        id="data_inicio_homologacao_modal"
                                        value="<?php echo $this->input->post('data_inicio_homologacao_modal') ?>"
                                        placeholder=""
                                    />
                                <?php else : ?>
                                    <input
                                        type="text"
                                        class="form-control datetimepicker"
                                        name="data_inicio_homologacao_modal"
                                        id="data_inicio_homologacao_modal"
                                        value="<?php echo $this->input->post('data_inicio_homologacao_modal') ?>"
                                        placeholder=""
                                    />
                                <?php endif; ?>

                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label for="data_fim_homologacao_modal">Data Final Homologação</label>
                        <div class="form-group" style="width: 100%">
                            <div class="input-group date datetimepicker" style="width: 100%" id="datepicker_fim_homologacao_modal">
                                <?php if ($this->input->post('data_fim_homologacao_modal') !== false) : ?>
                                    <input
                                    type="text"
                                    class="form-control datetimepicker"
                                    name="data_fim_homologacao_modal"
                                    id="data_fim_homologacao_modal"
                                    value="<?php echo $this->input->post('data_fim_homologacao_modal') ?>"
                                    placeholder=""
                                />
                                <?php else : ?>
                                    <input
                                    type="text"
                                    class="form-control datetimepicker"
                                    name="data_fim_homologacao_modal"
                                    id="data_fim_homologacao_modal"
                                    value="<?php echo $this->input->post('data_fim_homologacao_modal') ?>"
                                    placeholder=""
                                />
                                <?php endif; ?>

                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<br />
<div class="row">
    <div class="col-sm-3"></div>
    <div class="col-sm-9 text-right">
        <div class="form-group">
            <a href="<?php echo site_url('cadastros/mestre_itens?reset_filters=1'); ?>" class="btn btn-link pull-right">Limpar</a>
        </div>
        <div class="form-group">
            <?php if (customer_has_role('inclusao_itens', sess_user_id())) : ?>
                <a id="btn-novo-item" class="btn btn-default">
                    <i class="glyphicon glyphicon-plus"></i> Novo item
                </a>
            <?php endif; ?>
            <button type="button" class="btn btn-default btn-edit-item"><i class="glyphicon glyphicon-edit"></i> Editar</button>
            <button type="button" class="btn btn-danger btn-remove-item"><i class="glyphicon glyphicon-trash"></i> Excluir</button>
        </div>
    </div>
</div>
<br>
<div class="row">
    <div class="col-sm-12">
        <div class="form-group" style="width: 100%;">
            <button id="pesquisar-btn" class="btn btn-primary btn-block" type="submit">
                <i class="glyphicon glyphicon-search"></i> Pesquisar
                <img id="loading-img" src="<?php echo base_url('assets/img/loading_ajax.gif') ?>" width="10px" height="10px" style="display: none;">
            </button>
        </div>
    </div>
</div>

<?php echo form_close() ?>

<div id="loading-overlay">
    <div id="loading-message">Carregando...</div>
</div>
<script>
    $(document).ready(function() {
        $('#search_form_id').submit(function() {
            $('#pesquisar-btn').prop('disabled', true); // Desabilita o botão
            $('#loading-img').show(); // Exibe a imagem de carregamento
            $('#loading-overlay').show();
        });

        <?php
        $minha_variavel = $this->input->post('data_ini');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_ini').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_ini')))) ?>');
        <?php } ?>

        <?php
        $minha_variavel = $this->input->post('data_fim');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_fim').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_fim')))) ?>');
        <?php } ?>

        <?php
        $minha_variavel = $this->input->post('data_inicio_modificacao_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_inicio_modificacao_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_inicio_modificacao_modal')))) ?>');
        <?php } ?>

        <?php
        $minha_variavel = $this->input->post('data_fim_modificacao_modal');
        if (strpos($minha_variavel, '-') !== false) {
        ?>
            $('#data_fim_modificacao_modal').val('<?php echo date('d/m/Y', strtotime(str_replace('-', '/', $this->input->post('data_fim_modificacao_modal')))) ?>');
        <?php } ?>
    });

    /*
    Implementação para carregar os filtros via ajax
    */
    const base_url = '<?php echo base_url(); ?>';
    let eventosSelecionados = <?php echo json_encode($this->item_model->get_state('filter.evento')) ?> || [];
    let sistemasOrigemSelecionados = <?php echo json_encode($this->item_model->get_state('filter.sistema_origem_modal')) ?> || [];
    let exIpiSelecionados = <?php echo json_encode($this->item_model->get_state('filter.ex_ipi_modal')) ?> || [];
    let exIiSelecionados = <?php echo json_encode($this->item_model->get_state('filter.ex_ii_modal')) ?> || [];
    let prioridadesSelecionadas = <?php echo json_encode($this->item_model->get_state('filter.prioridade')) ?> || [];
    let ncmsSelecionados = <?php echo json_encode($this->item_model->get_state('filter.ncm_proposta_modal')) ?> || [];
    let ownersSelecionados = <?php echo json_encode($this->item_model->get_state('filter.owner')) ?> || [];
</script>
<script type="text/javascript" src="<?php echo base_url('assets/js/cadastros/mestre_itens.js'); ?>"></script>

<style>
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #loading-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 24px;
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const botao = document.getElementById('btn-novo-item');

        botao.addEventListener('click', function() {
            verifica_saldo();
        });

        function verifica_saldo() {
            fetch("<?php echo site_url('cadastros/mestre_itens/verifica_saldo'); ?>", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    body: ""
                })
                .then(response => response.json())
                .then(data => {
                    const saldo = data.saldo;
                    const habilitarUsoFranquia = data.habilitar_uso_franquia;
                    const habilitarBloqueio = data.habilitar_bloqueio;
                    if (habilitarUsoFranquia == 0 || (habilitarUsoFranquia == 1 && habilitarBloqueio == 0)) {
                        window.location.href = "<?php echo site_url('cadastros/mestre_itens/novo'); ?>";
                    }
                    if (saldo > 0) {
                        window.location.href = "<?php echo site_url('cadastros/mestre_itens/novo'); ?>";
                    } else if (habilitarUsoFranquia == 1 && habilitarBloqueio == 1) {

                        swal('Atenção', 'Você não possui saldo disponível para criar novos itens. Entre em contato com o suporte.', 'error');

                    }
                })
                .catch(error => {
                    console.error("Erro na requisição:", error);
                    alert("Erro ao verificar saldo.");
                });
        }
    });
</script>
